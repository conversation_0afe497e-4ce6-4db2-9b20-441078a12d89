"""
增强版实时通知服务 (NotificationService V2.0)

基于推送.md文档要求实现的核心通知服务，负责：
1. 将业务事件发布到Redis的Pub/Sub频道
2. 同步更新Redis中的持久化任务状态 
3. 支持完整的任务生命周期管理
4. 提供标准化的SSE事件格式
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from src.database.redis_client import RedisClient

logger = logging.getLogger(__name__)


class NotificationService:
    """
    增强版实时通知服务
    
    实现事件驱动架构，通过Redis Pub/Sub解耦业务逻辑与推送机制，
    同时维护持久化的任务状态以支持可观测性和恢复能力。
    """
    
    def __init__(self, redis_client: RedisClient, task_ttl: int = 3600):
        """
        初始化通知服务
        
        Args:
            redis_client: Redis客户端实例
            task_ttl: 任务状态TTL，默认1小时
        """
        self.redis = redis_client.client if hasattr(redis_client, 'client') else redis_client
        self.task_ttl = task_ttl
        self.logger = logger

    async def _publish_event(self, task_id: str, event_data: Dict[str, Any]):
        """
        发布事件到Redis Pub/Sub频道
        
        Args:
            task_id: 任务ID
            event_data: 事件数据
        """
        channel = f"task_channel:{task_id}"
        message = json.dumps(event_data, ensure_ascii=False)
        await self.redis.publish(channel, message)
        self.logger.debug(f"事件已发布到频道 {channel}: {event_data.get('event', 'unknown')}")

    async def _update_task_status(self, task_id: str, updates: Dict[str, Any]):
        """
        更新Redis HASH中的任务状态
        
        Args:
            task_id: 任务ID
            updates: 更新的状态字段
        """
        key = f"task_status:{task_id}"
        updates["last_updated"] = datetime.utcnow().isoformat()
        
        # 确保所有值都可序列化
        serializable_updates = {}
        for field, value in updates.items():
            if value is None:
                serializable_updates[field] = ""
            elif isinstance(value, dict) or isinstance(value, list):
                serializable_updates[field] = json.dumps(value, ensure_ascii=False)
            else:
                serializable_updates[field] = str(value)
        
        await self.redis.hset(key, mapping=serializable_updates)
        # 每次更新都刷新TTL，防止正常任务过期
        await self.redis.expire(key, self.task_ttl)
        self.logger.debug(f"任务状态已更新: {task_id}, TTL: {self.task_ttl}s")

    async def initialize_task(self, task_id: str):
        """
        初始化任务状态并设置TTL
        
        Args:
            task_id: 任务ID
        """
        initial_state = {
            "overall_status": "pending",
            "current_step": "initialization",
            "steps_status": json.dumps({})
        }
        
        await self._update_task_status(task_id, initial_state)
        self.logger.info(f"任务 {task_id} 状态已初始化，TTL: {self.task_ttl}s")

    async def notify_step_start(self, task_id: str, step_name: str, title: str, message: str):
        """
        推送步骤开始事件
        
        Args:
            task_id: 任务ID
            step_name: 步骤名称（如 'core_intent_analysis'）
            title: 显示标题
            message: 状态消息
        """
        # 构建标准SSE事件格式
        event = {
            "event": "step_start",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "title": title,
                "message": message
            }
        }
        
        # 发布事件
        await self._publish_event(task_id, event)
        
        # 更新任务状态
        status_update = {
            "overall_status": "running",
            "current_step": step_name
        }
        
        # 更新步骤状态
        key = f"task_status:{task_id}"
        steps_status_raw = await self.redis.hget(key, "steps_status")
        steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
        steps_status[step_name] = {
            "status": "running",
            "start_time": datetime.utcnow().isoformat(),
            "title": title,
            "message": message
        }
        status_update["steps_status"] = json.dumps(steps_status)
        
        await self._update_task_status(task_id, status_update)
        self.logger.info(f"步骤开始: {step_name} ({task_id}) - {title}")

    async def notify_step_end(self, task_id: str, step_name: str, status: str, result: Dict[str, Any] = None):
        """
        推送步骤结束事件
        
        Args:
            task_id: 任务ID
            step_name: 步骤名称
            status: 状态（'success' 或 'failed'）
            result: 步骤结果数据
        """
        # 构建标准SSE事件格式
        event = {
            "event": "step_end",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "status": status,
                "result": result or {}
            }
        }
        
        # 发布事件
        await self._publish_event(task_id, event)
        
        # 更新步骤状态
        key = f"task_status:{task_id}"
        steps_status_raw = await self.redis.hget(key, "steps_status")
        steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
        if step_name in steps_status:
            steps_status[step_name]["status"] = status
            steps_status[step_name]["end_time"] = datetime.utcnow().isoformat()
            if result:
                steps_status[step_name]["result"] = result
        
        await self._update_task_status(task_id, {"steps_status": json.dumps(steps_status)})
        self.logger.info(f"步骤结束: {step_name} ({task_id}) - {status}")

    async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        """
        推送规划完成事件
        
        Args:
            task_id: 任务ID
            final_data: 最终结果数据
        """
        # 发布完成事件
        complete_event = {"event": "complete", "data": final_data}
        await self._publish_event(task_id, complete_event)
        
        # 发布流结束信号
        eos_event = {"event": "eos"}
        await self._publish_event(task_id, eos_event)
        
        # 更新最终状态
        status_update = {
            "overall_status": "completed",
            "current_step": "finished",
            "final_result": json.dumps(final_data)
        }
        await self._update_task_status(task_id, status_update)
        self.logger.info(f"任务完成: {task_id}")

    async def notify_error(self, task_id: str, error_message: str, step_name: str = "unknown"):
        """
        推送错误事件
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
            step_name: 出错的步骤名称
        """
        # 发布错误事件
        error_event = {
            "event": "error",
            "data": {
                "step_name": step_name,
                "message": error_message
            }
        }
        await self._publish_event(task_id, error_event)
        
        # 发布流结束信号
        eos_event = {"event": "eos"}
        await self._publish_event(task_id, eos_event)
        
        # 更新错误状态
        status_update = {
            "overall_status": "failed",
            "current_step": step_name,
            "error_info": error_message
        }
        await self._update_task_status(task_id, status_update)
        self.logger.error(f"任务失败: {task_id}, 错误: {error_message}")

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态字典，如果任务不存在则返回None
        """
        key = f"task_status:{task_id}"
        data = await self.redis.hgetall(key)
        
        if not data:
            return None
        
        # 反序列化JSON字段
        result = {}
        for field, value in data.items():
            if field in ['steps_status', 'final_result'] and value:
                try:
                    result[field] = json.loads(value)
                except json.JSONDecodeError:
                    result[field] = value
            else:
                result[field] = value if value != "" else None
        
        return result

    async def store_analysis_result(self, task_id: str, analysis_data: Dict[str, Any]):
        """
        存储完整的意图分析结果到Redis

        按照数据流转与记忆体系.md文档要求的Redis Hash结构存储数据

        Args:
            task_id: 任务ID
            analysis_data: 分析结果数据，包含四个分析步骤的结果
        """
        try:
            # 构建符合文档要求的Redis Hash结构
            hash_data = {
                "parsed_intent": json.dumps(analysis_data.get("core_intent", {}), ensure_ascii=False),
                "user_profile_snapshot": json.dumps(analysis_data.get("user_profile", {}), ensure_ascii=False),
                "food_preferences": json.dumps(analysis_data.get("food_preferences", {}), ensure_ascii=False),
                "attraction_preferences": json.dumps(analysis_data.get("attraction_preferences", {}), ensure_ascii=False),
                "accommodation_preferences": json.dumps(analysis_data.get("accommodation_preferences", {}), ensure_ascii=False),
                "analysis_completed": "true",
                "analysis_completed_at": datetime.utcnow().isoformat()
            }

            await self._update_task_status(task_id, hash_data)
            self.logger.info(f"意图分析结果已存储: {task_id}")

        except Exception as e:
            self.logger.error(f"存储分析结果失败: {task_id}, 错误: {str(e)}")
            raise

    async def get_analysis_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        从Redis读取意图分析结果

        Args:
            task_id: 任务ID

        Returns:
            分析结果字典，如果不存在则返回None
        """
        try:
            key = f"task_status:{task_id}"
            data = await self.redis.hgetall(key)

            if not data or data.get("analysis_completed") != "true":
                self.logger.warning(f"任务 {task_id} 的分析结果不存在或未完成")
                return None

            # 解析JSON字段
            result = {
                "core_intent": json.loads(data.get("parsed_intent", "{}")),
                "user_profile": json.loads(data.get("user_profile_snapshot", "{}")),
                "food_preferences": json.loads(data.get("food_preferences", "{}")),
                "attraction_preferences": json.loads(data.get("attraction_preferences", "{}")),
                "accommodation_preferences": json.loads(data.get("accommodation_preferences", "{}"))
            }

            self.logger.info(f"成功读取分析结果: {task_id}")
            return result

        except Exception as e:
            self.logger.error(f"读取分析结果失败: {task_id}, 错误: {str(e)}")
            return None

    async def store_final_result(self, task_id: str, final_data: Dict[str, Any]):
        """
        存储最终规划结果

        Args:
            task_id: 任务ID
            final_data: 最终规划结果
        """
        try:
            hash_data = {
                "final_result": json.dumps(final_data, ensure_ascii=False),
                "planning_completed": "true",
                "planning_completed_at": datetime.utcnow().isoformat()
            }

            await self._update_task_status(task_id, hash_data)
            self.logger.info(f"最终规划结果已存储: {task_id}")

        except Exception as e:
            self.logger.error(f"存储最终结果失败: {task_id}, 错误: {str(e)}")
            raise

    async def initialize_task_with_three_way_setup(self, task_id: str, user_id: str, query: str):
        """
        按照数据流转与记忆体系.md要求进行三方占坑初始化

        Args:
            task_id: 任务ID
            user_id: 用户ID
            query: 用户查询
        """
        try:
            # 导入数据库相关模块
            from src.database.mysql_client import get_db
            from src.database.mongodb_client import get_mongo_client
            from src.models.mysql_crud import ai_planning_session_crud
            from datetime import datetime

            # 1. MySQL占坑 - 在ai_planning_sessions表中插入记录
            async with get_db() as db:
                await ai_planning_session_crud.create_session(db, session_data={
                    'id': task_id,
                    'user_id': int(user_id) if user_id.isdigit() else 1,  # 处理guest用户
                    'status': 'PROCESSING',
                    'user_input': {"query": query},
                    'created_at': datetime.now()
                })

            # 2. MongoDB占坑 - 在ai_interaction_logs集合中插入骨架文档
            mongo_client = await get_mongo_client()
            await mongo_client.create_interaction_log({
                "user_id": user_id,
                "interaction_id": task_id,
                "application_source": "dh_tripplanner",
                "status": "PROCESSING"
            })

            # 3. Redis创建"作战室" - 初始化任务状态Hash
            initial_state = {
                "task_id": task_id,
                "user_id": user_id,
                "status": "PROCESSING",
                "created_at": datetime.utcnow().isoformat(),
                "raw_user_query": query,
                "overall_status": "pending",
                "current_step": "initialization",
                "steps_status": json.dumps({})
            }

            await self._update_task_status(task_id, initial_state)
            self.logger.info(f"三方占坑完成: {task_id}")

        except Exception as e:
            self.logger.error(f"三方占坑失败: {task_id}, 错误: {str(e)}")
            raise

    async def archive_task_data(self, task_id: str):
        """
        任务完成后的数据归档

        将Redis中的完整数据归档到MySQL和MongoDB
        """
        try:
            # 导入数据库相关模块
            from src.database.mysql_client import get_db
            from src.database.mongodb_client import get_mongo_client
            from src.models.mysql_crud import ai_planning_session_crud

            # 1. 从Redis获取完整执行记录
            key = f"task_status:{task_id}"
            redis_data = await self.redis.hgetall(key)

            if not redis_data:
                self.logger.warning(f"任务 {task_id} 的Redis数据不存在，跳过归档")
                return

            # 2. 更新MongoDB - 用Redis数据填充完整日志
            mongo_client = await get_mongo_client()
            await mongo_client.update_interaction_log(task_id, {
                "status": redis_data.get("overall_status", "unknown"),
                "business_steps_log": json.loads(redis_data.get("steps_status", "{}")),
                "final_result": json.loads(redis_data.get("final_result", "{}")),
                "completed_at": redis_data.get("planning_completed_at") or datetime.utcnow().isoformat()
            })

            # 3. 更新MySQL - 更新ai_planning_sessions状态
            status = "SUCCESS" if redis_data.get("overall_status") == "completed" else "FAILED"
            async with get_db() as db:
                await ai_planning_session_crud.update_session(db, id=task_id, session_data={
                    'status': status,
                    'planning_log': redis_data.get("steps_status", ""),
                    'raw_llm_output': json.loads(redis_data.get("final_result", "{}")),
                    'completed_at': datetime.now()
                })

            # 4. 清理Redis资源
            await self.redis.delete(key)
            self.logger.info(f"任务数据归档完成: {task_id}")

        except Exception as e:
            self.logger.error(f"任务数据归档失败: {task_id}, 错误: {str(e)}")
            # 归档失败不应该影响用户体验，只记录错误

    async def cleanup_task(self, task_id: str) -> bool:
        """
        清理任务数据

        Args:
            task_id: 任务ID

        Returns:
            是否成功清理
        """
        try:
            key = f"task_status:{task_id}"
            await self.redis.delete(key)
            self.logger.info(f"任务数据已清理: {task_id}")
            return True
        except Exception as e:
            self.logger.error(f"清理任务数据失败: {task_id}, 错误: {str(e)}")
            return False