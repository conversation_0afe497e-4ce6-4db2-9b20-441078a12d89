"""
旅行规划API接口

提供旅行规划的RESTful API和SSE流式接口，支持实时进度反馈。
"""
import asyncio
import json
import uuid
import time
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse

from src.models.travel_planner import (
    TravelPlanRequest, TravelItinerary, StreamEvent, EventType, UserProfile
)
from src.models.mysql_crud import (
    ai_planning_session_crud
)
# 使用LangGraph重构的Agent
# 移除旧的LangGraph Agent导入
# from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
# V2 服务导入
from src.agents.travel_planner_lg.state import TravelPlanState
from src.services.notification_service import NotificationService
from src.database.mongodb_client import get_mongo_client
from src.database.mysql_client import get_db
from src.database.redis_client import get_redis_client
from src.core.config import get_settings
from src.core.logger import get_logger

logger = get_logger("travel_planner_api")
router = APIRouter(prefix="/api/travel", tags=["旅行规划"])


class PlanRequest(BaseModel):
    """规划请求模型"""
    query: str
    user_id: str
    session_id: Optional[str] = None


class PlanResponse(BaseModel):
    """规划响应模型"""
    trace_id: str
    status: str
    message: str


@router.post("/plan", response_model=PlanResponse)
async def create_travel_plan(request: PlanRequest):
    """
    创建旅行规划任务
    
    创建一个新的旅行规划任务，返回trace_id用于后续查询进度。
    """
    try:
        trace_id = str(uuid.uuid4())
        
        # 记录规划请求
        # 在 ai_planning_sessions 表中插入一条新记录
        async with get_db() as db:
            await ai_planning_session_crud.create_session(db, session_data={
                'id': trace_id,
                'user_id': request.user_id,
                # 'session_id': request.session_id,
                'user_input': request.query,
                'created_at': datetime.now()
            })
        # 在 ai_interaction_logs 集合中插入一个"骨架"文档
        mongo_client = await get_mongo_client()
        await mongo_client.create_interaction_log({
            "user_id": request.user_id,
            "interaction_id": trace_id,
            "status": "PROCESSING"
        })
        await mongo_client.log_analytics({
            "event_type": "plan_request",
            "user_id": request.user_id,
            "trace_id": trace_id,
            "properties": {
                "query": request.query,
                "session_id": request.session_id
            }
        })
        
        logger.info(f"创建旅行规划任务: {trace_id}", extra={
            "trace_id": trace_id,
            "user_id": request.user_id,
            "query": request.query
        })
        
        return PlanResponse(
            trace_id=trace_id,
            status="created",
            message="规划任务已创建，请使用SSE接口获取实时进度"
        )
        
    except Exception as e:
        logger.error(f"创建规划任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建规划任务失败")


@router.get("/plan/{trace_id}/stream")
async def stream_travel_plan(trace_id: str, user_id: str, query: str, phase: str = "analysis"):
    """
    流式获取旅行规划进度
    
    通过Server-Sent Events (SSE) 实时获取旅行规划的进度和结果。
    """
    
    async def event_generator():
        """事件生成器"""
        try:
            # 创建LangGraph旅行规划Agent
            # 移除旧的LangGraph Agent导入
            # agent = TravelPlannerAgentLangGraph(enable_interaction_hooks=True)

            # 创建规划请求
            plan_request = TravelPlanRequest(
                user_id=user_id,
                query=query,
                trace_id=trace_id
            )

            # 发送开始事件
            start_event_data = {
                "trace_id": trace_id,
                "event_type": "start",
                "payload": {
                    "message": "开始规划旅行行程"
                },
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(start_event_data, ensure_ascii=False)}\n\n"

            # 执行规划并流式返回结果
            # LangGraph Agent使用不同的接口
            if phase == "analysis":
                # 分析阶段：使用交互式流式规划
                stream_generator = agent.plan_travel_stream_interactive(
                    user_id=user_id,
                    query=query,
                    session_id=trace_id
                )
            else:
                # 规划阶段：使用全自动流式规划
                stream_generator = agent.plan_travel_stream_automatic(
                    user_id=user_id,
                    query=query,
                    session_id=trace_id
                )

            async for sse_event_str in stream_generator:
                # LangGraph Agent已经返回格式化的SSE字符串，直接发送
                yield sse_event_str

                # 尝试解析事件以检查是否需要保存到数据库
                try:
                    # 从SSE字符串中提取JSON数据
                    if sse_event_str.startswith("data: "):
                        json_str = sse_event_str[6:].strip()
                        if json_str and json_str != "\n":
                            event_data = json.loads(json_str)

                            # 如果是最终行程，保存到数据库
                            if event_data.get("event_type") == "final_itinerary":
                                try:
                                    mongo_client = await get_mongo_client()
                                    # 转换TravelItinerary数据为ItineraryDocument兼容格式
                                    itinerary_data = event_data.get("data", {}).copy()

                                    # 从summary中提取字段到顶层
                                    if "summary" in itinerary_data:
                                        summary = itinerary_data["summary"]
                                        itinerary_data["title"] = summary.get("title", "未命名行程")
                                        itinerary_data["destination_city"] = summary.get("destination_city", "未知目的地")
                                        itinerary_data["days"] = summary.get("days", 3)
                                        itinerary_data["tags"] = summary.get("tags", [])

                                    await mongo_client.create_itinerary(itinerary_data)
                                    logger.info(f"行程已保存到数据库: {trace_id}")
                                except Exception as e:
                                    logger.error(f"保存行程失败: {str(e)}")
                except Exception as e:
                    # 忽略解析错误，继续处理
                    pass

                # 添加小延迟，避免过快发送
                await asyncio.sleep(0.1)
            
            # 发送完成事件
            complete_event_data = {
                "trace_id": trace_id,
                "event_type": "complete",
                "payload": {
                    "message": "旅行规划完成"
                },
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(complete_event_data, ensure_ascii=False)}\n\n"
            
        except Exception as e:
            logger.error(f"规划过程中发生错误: {str(e)}", extra={
                "trace_id": trace_id,
                "error": str(e)
            })
            
            # 发送错误事件
            error_event_data = {
                "trace_id": trace_id,
                "event_type": "error",
                "payload": {
                    "error_message": str(e)
                },
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(error_event_data, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@router.post("/plan/{trace_id}/start_planning")
async def start_planning_phase(
    trace_id: str,
    analysis_result: Dict[str, Any]
):
    """
    启动规划阶段

    基于分析阶段的结果，启动详细的行程规划阶段。

    Args:
        trace_id: 会话ID
        analysis_result: 分析阶段的结果数据
    """
    try:
        logger.info(f"启动规划阶段 - Session: {trace_id}")

        # 创建Agent实例
        # 移除旧的LangGraph Agent导入
        # agent = TravelPlannerAgentLangGraph()

        # 启动规划阶段的流式处理
        async def generate_planning_stream():
            try:
                async for event in agent.start_planning_phase(trace_id, analysis_result):
                    yield event
            except Exception as e:
                logger.error(f"规划阶段流式处理失败: {str(e)}")
                error_event = {
                    "event_type": "error",
                    "payload": {
                        "error_message": f"规划失败: {str(e)}",
                        "stage": "planning"
                    }
                }
                yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

        return StreamingResponse(
            generate_planning_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )

    except Exception as e:
        logger.error(f"启动规划阶段失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动规划失败: {str(e)}")


@router.get("/plan/{trace_id}")
async def get_travel_plan(trace_id: str):
    """
    获取旅行规划结果
    
    根据trace_id获取完整的旅行规划结果。
    """
    try:
        mongo_client = await get_mongo_client()
        itinerary = await mongo_client.get_itinerary(trace_id)
        
        if not itinerary:
            raise HTTPException(status_code=404, detail="未找到指定的行程")
        
        return {
            "code": 200,
            "data": itinerary,
            "message": "获取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取行程失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取行程失败")


@router.get("/user/{user_id}/plans")
async def get_user_plans(
    user_id: str,
    limit: int = 20,
    skip: int = 0,
    status: Optional[str] = None
):
    """
    获取用户的旅行规划列表
    
    分页获取指定用户的所有旅行规划。
    """
    try:
        mongo_client = await get_mongo_client()
        itineraries = await mongo_client.get_user_itineraries(
            user_id=user_id,
            limit=limit,
            skip=skip,
            status=status
        )
        
        return {
            "code": 200,
            "data": {
                "items": itineraries,
                "total": len(itineraries),
                "limit": limit,
                "skip": skip
            },
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取用户行程列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取行程列表失败")


@router.put("/plan/{trace_id}")
async def update_travel_plan(trace_id: str, update_data: Dict[str, Any]):
    """
    更新旅行规划
    
    更新指定的旅行规划信息。
    """
    try:
        mongo_client = await get_mongo_client()
        success = await mongo_client.update_itinerary(trace_id, update_data)
        
        if not success:
            raise HTTPException(status_code=404, detail="未找到指定的行程")
        
        return {
            "code": 200,
            "message": "更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新行程失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新行程失败")


@router.post("/feedback")
async def submit_feedback(feedback_data: Dict[str, Any]):
    """
    提交反馈
    
    用户对旅行规划结果的反馈，用于改进算法。
    """
    try:
        mongo_client = await get_mongo_client()
        
        # 创建反馈记录
        feedback_id = await mongo_client.create_memory({
            "user_id": feedback_data.get("user_id"),
            "memory_type": "feedback",
            "content": feedback_data.get("content", ""),
            "context": feedback_data,
            "importance_score": 2.0,  # 反馈的重要性较高
            "related_itinerary_id": feedback_data.get("trace_id")
        })
        
        # 记录分析数据
        await mongo_client.log_analytics({
            "event_type": "feedback_submitted",
            "user_id": feedback_data.get("user_id"),
            "properties": feedback_data
        })
        
        return {
            "code": 200,
            "data": {"feedback_id": feedback_id},
            "message": "反馈提交成功"
        }
        
    except Exception as e:
        logger.error(f"提交反馈失败: {str(e)}")
        raise HTTPException(status_code=500, detail="提交反馈失败")


@router.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        mongo_client = await get_mongo_client()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "mongodb": "connected",
                "agent": "ready"
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }


@router.post("/weather/forecast")
async def get_weather_forecast(request: dict):
    """
    获取天气预报信息

    Args:
        request: 包含city和days的请求数据
    """
    try:
        city = request.get("city")
        days = request.get("days", 3)

        if not city:
            raise HTTPException(status_code=400, detail="城市参数不能为空")

        # 导入高德地图工具
        from tools.Amap.map_tool import MapTool
        from src.core.config import get_settings

        config = get_settings()
        map_tool = MapTool(api_key=config.amap_api_key)

        # 查询天气信息
        weather_result = map_tool.get_weather_info(city=city)

        if weather_result.get("status") == "1":
            # 处理天气数据
            weather_data = []

            if weather_result.get("lives"):
                # 实时天气
                live_weather = weather_result["lives"][0]
                weather_data.append({
                    "date": "今天",
                    "temperature": f"{live_weather.get('temperature', '--')}°C",
                    "weather": live_weather.get("weather", "未知"),
                    "humidity": live_weather.get("humidity", "--"),
                    "wind": live_weather.get("winddirection", "") + live_weather.get("windpower", "")
                })

            if weather_result.get("forecasts"):
                # 预报天气
                forecasts = weather_result["forecasts"][0].get("casts", [])
                for i, forecast in enumerate(forecasts[:days]):
                    weather_data.append({
                        "date": forecast.get("date", f"第{i+1}天"),
                        "temperature": f"{forecast.get('nighttemp', '--')}°C~{forecast.get('daytemp', '--')}°C",
                        "weather": f"{forecast.get('dayweather', '')} 转 {forecast.get('nightweather', '')}",
                        "wind": forecast.get("daywind", "")
                    })

            return {
                "status": "success",
                "city": city,
                "weather": weather_data,
                "description": f"已获取{city}的天气信息"
            }
        else:
            return {
                "status": "error",
                "weather": [],
                "description": f"无法获取{city}的天气信息"
            }

    except Exception as e:
        logger.error(f"天气查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"天气查询失败: {str(e)}")


@router.get("/user_profile")
async def get_user_profile(user_id: str = "1"):
    """
    获取用户画像信息

    Args:
        user_id: 用户ID，默认为"1"

    Returns:
        用户画像数据，包括基本信息、偏好标签、预算偏好等
    """
    try:
        # 直接从数据库获取用户画像和记忆
        from src.models.mysql_crud import user_memory_crud, user_summary_crud, itinerary_crud
        from src.database.mysql_client import get_db

        async with get_db() as db:
            # 获取用户记忆
            user_memories = await user_memory_crud.get_by_user(db, user_id=user_id)

            # 获取用户画像总结
            user_summary = await user_summary_crud.get_by_user(db, user_id=user_id)

            # 获取历史行程
            itineraries = await itinerary_crud.get_by_user(db, user_id=user_id)

            # 构建完整的用户画像
            preferences = {}
            tags = []

            # 从用户总结中提取偏好
            if user_summary:
                summary_data = user_summary.summary_data if hasattr(user_summary, 'summary_data') else {}
                if isinstance(summary_data, dict):
                    preferences.update(summary_data.get('preferences', {}))
                    tags.extend(summary_data.get('tags', []))

            # 从历史记忆中提取标签和偏好
            for memory in user_memories:
                if hasattr(memory, 'keywords') and memory.keywords:
                    tags.extend(memory.keywords)
                if hasattr(memory, 'content') and memory.content:
                    # 简单的关键词提取
                    content = memory.content.lower()
                    if '亲子' in content or '孩子' in content:
                        tags.append('亲子')
                    if '历史' in content or '文化' in content:
                        tags.append('历史文化')
                    if '美食' in content:
                        tags.append('美食')
                    if '自然' in content or '风景' in content:
                        tags.append('自然风光')

            # 设置默认偏好
            if not preferences:
                preferences = {
                    'travel_style': '休闲',
                    'budget_preference': '中等',
                    'preferred_trip_duration': 3,
                    'age_group': '25-35岁',
                    'gender': '未知',
                    'occupation': '白领',
                    'travel_companion': '个人'
                }

            # 去重标签
            tags = list(set(tags))[:15]
            if not tags:
                tags = ['休闲', '舒适', '性价比']

            user_profile_data = {
                'preferences': preferences,
                'tags': tags,
                'budget_preference': preferences.get('budget_preference', '中等'),
                'travel_style': preferences.get('travel_style', '休闲')
            }

        if not user_profile_data:
            # 返回默认用户画像
            return {
                "basic_info": {
                    "age": "25-35岁",
                    "gender": "未知",
                    "occupation": "白领",
                    "travel_companion": "个人"
                },
                "tags": ["休闲", "舒适"],
                "budget_preference": "中等",
                "preferences": {
                    "travel_style": "休闲",
                    "season": "春季",
                    "duration": "3天"
                },
                "recommendation_reason": "基于默认偏好为您推荐适合的旅行方案。"
            }

        # 解析用户画像数据
        preferences = user_profile_data.get('preferences', {})
        tags = user_profile_data.get('tags', [])

        # 构建基本信息
        basic_info = {
            "age": preferences.get('age_group', '25-35岁'),
            "gender": preferences.get('gender', '未知'),
            "occupation": preferences.get('occupation', '白领'),
            "travel_companion": preferences.get('travel_companion', '个人')
        }

        # 构建返回数据
        profile_response = {
            "basic_info": basic_info,
            "tags": tags[:8],  # 限制标签数量
            "budget_preference": user_profile_data.get('budget_preference', '中等'),
            "preferences": {
                "travel_style": user_profile_data.get('travel_style', '休闲'),
                "season": preferences.get('preferred_season', '春季'),
                "duration": f"{preferences.get('preferred_trip_duration', 3)}天"
            },
            "recommendation_reason": f"根据您的{len(user_memories)}次历史旅行记录和个人偏好，为您推荐最适合的旅行方案。"
        }

        logger.info(f"成功获取用户{user_id}的画像信息")
        return profile_response

    except Exception as e:
        logger.error(f"获取用户画像失败: {str(e)}", exc_info=True)
        # 返回默认画像作为后备
        return {
            "basic_info": {
                "age": "25-35岁",
                "gender": "未知",
                "occupation": "白领",
                "travel_companion": "个人"
            },
            "tags": ["休闲", "舒适"],
            "budget_preference": "中等",
            "preferences": {
                "travel_style": "休闲",
                "season": "春季",
                "duration": "3天"
            },
            "recommendation_reason": "基于默认偏好为您推荐适合的旅行方案。"
        }

# === V2 流式API端点（基于Redis Pub/Sub事件驱动架构）===

async def redis_event_generator(task_id: str):
    """
    监听Redis频道并生成SSE事件
    
    基于推送.md文档V2.2架构实现的事件监听器
    """
    redis_client = await get_redis_client()
    await redis_client.connect()
    
    channel = f"task_channel:{task_id}"
    logger.info(f"开始监听Redis频道: {channel}")
    
    try:
        # 创建Pub/Sub客户端
        pubsub = redis_client.client.pubsub()
        await pubsub.subscribe(channel)
        
        timeout_counter = 0
        max_timeout = 300  # 5分钟超时
        
        while timeout_counter < max_timeout:
            # 获取消息，设置1秒超时
            message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
            
            if message and message['data']:
                # 重置超时计数器
                timeout_counter = 0
                
                try:
                    data = message['data'].decode('utf-8') if isinstance(message['data'], bytes) else message['data']
                    logger.info(f"[DEBUG] 收到Redis原始事件: {repr(data)}")

                    # Redis中存储的是纯JSON，直接yield给EventSourceResponse
                    # EventSourceResponse会自动添加"data: "前缀，所以这里只需要yield纯JSON
                    logger.info(f"[DEBUG] 收到Redis原始事件: {repr(data)}")
                    yield data
                    
                    # 检查是否是结束事件
                    if '"event": "eos"' in data:
                        logger.info(f"收到EOS事件，结束监听: {task_id}")
                        break
                        
                except Exception as e:
                    logger.error(f"处理Redis消息失败: {str(e)}")
                    continue
            else:
                # 增加超时计数器
                timeout_counter += 1
                await asyncio.sleep(0.1)
                
    except Exception as e:
        logger.error(f"Redis事件监听失败: {str(e)}")
        # 发送错误事件
        error_event = {
            "event": "error",
            "data": {"message": f"事件监听失败: {str(e)}"}
        }
        yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
        
    finally:
        try:
            await pubsub.unsubscribe(channel)
            logger.info(f"已取消订阅Redis频道: {channel}")
        except Exception as e:
            logger.warning(f"取消订阅失败: {str(e)}")


async def execute_intent_analysis(
    task_id: str,
    user_id: str,
    query: str,
    notification_service
):
    """
    执行V2意图分析的后台任务，只执行前四个分析步骤

    Args:
        task_id: 任务ID
        user_id: 用户ID
        query: 用户查询
        notification_service: 通知服务实例
    """
    try:
        logger.info(f"开始执行V2意图分析任务: {task_id}")

        # 导入分析服务
        from src.agents.services.analysis_service import AnalysisService
        from src.agents.services.user_profile_service import UserProfileService

        analysis_service = AnalysisService()
        user_profile_service = UserProfileService()

        # 获取用户画像
        user_profile = await user_profile_service.get_user_profile(user_id)

        # 步骤1: 核心意图分析
        await notification_service.notify_step_start(
            task_id, "core_intent_analysis", "解析用户需求", "正在分析您的旅行意图和需求..."
        )

        core_intent = await analysis_service.analyze_core_intent(
            query, user_id, user_profile
        )

        destinations = core_intent.get("destinations", [])
        await notification_service.notify_step_end(
            task_id, "core_intent_analysis", "success", {
                "content": f"识别出{len(destinations)}个目的地：{', '.join(destinations)}",
                "destinations": destinations,
                "duration_days": core_intent.get('days', core_intent.get('duration_days', 2))
            }
        )

        # 步骤2: 美食偏好分析
        await notification_service.notify_step_start(
            task_id, "food_preferences_analysis", "美食偏好分析", "正在分析您的美食偏好..."
        )

        food_preferences = await analysis_service.analyze_food_preferences(
            core_intent, user_profile, []
        )

        await notification_service.notify_step_end(
            task_id, "food_preferences_analysis", "success", {
                "content": "美食偏好分析完成",
                "preferences": food_preferences
            }
        )

        # 步骤3: 景点偏好分析
        await notification_service.notify_step_start(
            task_id, "attraction_preferences_analysis", "景点偏好分析", "正在分析您的景点偏好..."
        )

        attraction_preferences = await analysis_service.analyze_attraction_preferences(
            core_intent, user_profile, []
        )

        await notification_service.notify_step_end(
            task_id, "attraction_preferences_analysis", "success", {
                "content": "景点偏好分析完成",
                "preferences": attraction_preferences
            }
        )

        # 步骤4: 住宿偏好分析
        await notification_service.notify_step_start(
            task_id, "accommodation_preferences_analysis", "住宿偏好分析", "正在分析您的住宿偏好..."
        )

        accommodation_preferences = await analysis_service.analyze_accommodation_preferences(
            core_intent, user_profile, []
        )

        await notification_service.notify_step_end(
            task_id, "accommodation_preferences_analysis", "success", {
                "content": "住宿偏好分析完成",
                "preferences": accommodation_preferences
            }
        )

        # 构建完整的分析结果
        analysis_result = {
            "core_intent": core_intent,
            "user_profile": user_profile,
            "food_preferences": food_preferences,
            "attraction_preferences": attraction_preferences,
            "accommodation_preferences": accommodation_preferences,
            "task_id": task_id,
            "user_id": user_id,
            "query": query
        }

        # 将分析结果存储到Redis中，供后续规划接口使用
        await notification_service.store_analysis_result(task_id, analysis_result)

        # 发送分析完成事件
        await notification_service.notify_final_result(task_id, {
            "analysis_complete": True,
            "task_id": task_id,  # 返回task_id供前端调用规划接口
            "message": "意图分析完成，可以开始规划行程"
        })

        logger.info(f"V2意图分析任务完成: {task_id}")

    except Exception as e:
        logger.error(f"V2意图分析任务失败: {task_id}, 错误: {str(e)}")
        await notification_service.notify_error(task_id, f"意图分析失败: {str(e)}")


async def execute_travel_planning(task_id: str, user_id: str, query: str):
    """
    V2架构后台规划任务执行函数

    使用LangGraph工作流和事件驱动架构执行规划
    """
    redis_client = await get_redis_client()
    settings = get_settings()
    notification_service = NotificationService(redis_client, task_ttl=settings.redis.task_ttl)

    try:
        logger.info(f"开始执行V2规划任务: {task_id}")

        # 导入LangGraph工作流
        from src.agents.travel_planner_lg.graph import TravelPlannerGraph

        # 创建LangGraph工作流实例
        graph = TravelPlannerGraph(enable_checkpointing=False)

        # 执行自动规划流程
        final_state = await graph.run_automatic(
            user_id=user_id,
            query=query,
            task_id=task_id,
            notification_service=notification_service
        )

        # 发送最终完成事件
        final_data = final_state.get("final_itinerary", {})
        if not final_data:
            # 如果没有最终行程，创建一个基本的响应
            final_data = {
                "title": f"旅行计划 - {task_id}",
                "message": "规划已完成",
                "task_id": task_id
            }

        await notification_service.notify_final_result(task_id, final_data)

        logger.info(f"V2规划任务完成: {task_id}")

    except Exception as e:
        logger.error(f"V2规划任务失败: {task_id}, 错误: {str(e)}")
        await notification_service.notify_error(task_id, f"规划执行失败: {str(e)}")


@router.post("/v2/intent/analyze", name="V2意图识别接口")
async def analyze_intent_v2(
    request_data: dict,
    redis_client = Depends(get_redis_client)
):
    """
    V2版意图识别接口，只执行分析阶段的四个步骤

    执行步骤：
    1. 核心意图分析 (core_intent_analysis)
    2. 美食偏好分析 (food_preferences_analysis)
    3. 景点偏好分析 (attraction_preferences_analysis)
    4. 住宿偏好分析 (accommodation_preferences_analysis)

    完成后前端显示"立即规划"按钮
    """
    user_id = request_data.get("user_id", "guest")
    query = request_data.get("query")

    if not query:
        raise HTTPException(status_code=400, detail="查询内容不能为空")

    try:
        # 生成统一格式的任务ID
        task_id = f"task_{user_id}_{int(time.time())}"

        # 获取配置
        settings = get_settings()
        task_ttl = settings.redis.task_ttl

        # 连接Redis
        await redis_client.connect()

        # 创建NotificationService并进行三方占坑初始化
        notification_service = NotificationService(redis_client, task_ttl=task_ttl)
        await notification_service.initialize_task_with_three_way_setup(task_id, user_id, query)

        logger.info(f"创建V2意图分析任务: {task_id}, 用户: {user_id}, 查询: {query}")

        # 启动后台意图分析任务
        asyncio.create_task(
            execute_intent_analysis(
                task_id=task_id,
                user_id=user_id,
                query=query,
                notification_service=notification_service
            )
        )

        # 返回基于Redis事件订阅的SSE流
        return EventSourceResponse(redis_event_generator(task_id))

    except Exception as e:
        logger.error(f"创建V2意图分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建意图分析任务失败: {str(e)}")


@router.post("/v2/plan/stream", name="流式旅行规划 V2")
async def plan_travel_stream_v2(
    request_data: dict,
    redis_client = Depends(get_redis_client)
):
    """
    V2版流式规划接口，基于Redis Pub/Sub实现事件驱动推送

    接收参数：
    - task_id: 意图分析阶段生成的任务ID（必需）
    - user_id: 用户ID（可选，用于验证）

    实现推送.md文档要求的完全解耦的事件驱动架构：
    1. 通过task_id从Redis读取意图分析结果
    2. 启动后台规划任务
    3. 通过Redis事件订阅返回SSE流
    4. 完成后进行数据归档
    """
    user_id = request_data.get("user_id", "guest")
    intent_task_id = request_data.get("task_id")  # 意图分析阶段的task_id

    if not intent_task_id:
        raise HTTPException(status_code=400, detail="task_id不能为空")

    try:
        # 连接Redis
        await redis_client.connect()

        # 创建NotificationService
        settings = get_settings()
        task_ttl = settings.redis.task_ttl
        notification_service = NotificationService(redis_client, task_ttl=task_ttl)

        # 从Redis读取意图分析结果
        analysis_result = await notification_service.get_analysis_result(intent_task_id)
        if not analysis_result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到task_id {intent_task_id} 的意图分析结果，请先完成意图分析"
            )

        # 生成新的规划任务ID
        planning_task_id = f"planning_{user_id}_{int(time.time())}"

        # 进行三方占坑初始化
        query = analysis_result.get("core_intent", {}).get("original_query", "")
        await notification_service.initialize_task_with_three_way_setup(planning_task_id, user_id, query)

        logger.info(f"创建V2规划任务: {planning_task_id}, 基于意图分析: {intent_task_id}")

        # 启动后台规划任务，使用从Redis读取的分析结果
        asyncio.create_task(
            execute_travel_planning_with_analysis(
                task_id=planning_task_id,
                user_id=user_id,
                query=query,
                analysis_result=analysis_result,
                notification_service=notification_service
            )
        )

        # 返回基于Redis事件订阅的SSE流
        return EventSourceResponse(redis_event_generator(planning_task_id))

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"创建V2流式规划失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建规划任务失败: {str(e)}")


@router.get("/v2/plan/{task_id}/status", name="查询任务状态 V2")
async def get_task_status_v2(
    task_id: str,
    redis_client = Depends(get_redis_client)
):
    """
    查询任务状态（基于Redis HASH持久化状态）
    
    支持运维监控和问题诊断，提供完整的任务状态可观测性
    """
    try:
        await redis_client.connect()
        notification_service = NotificationService(redis_client)
        
        status = await notification_service.get_task_status(task_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="任务不存在或已过期")
        
        return {
            "task_id": task_id,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="查询任务状态失败")


async def execute_travel_planning_with_analysis(
    task_id: str,
    user_id: str,
    query: str,
    analysis_result: dict,
    notification_service
):
    """
    执行V2旅行规划的后台任务（使用已有分析结果）

    Args:
        task_id: 任务ID
        user_id: 用户ID
        query: 用户查询
        analysis_result: 已有的分析结果
        notification_service: 通知服务实例
    """
    try:
        logger.info(f"开始执行V2规划任务(复用分析结果): {task_id}")

        # 导入LangGraph工作流
        from src.agents.travel_planner_lg.graph import TravelPlannerGraph

        # 创建LangGraph工作流实例
        graph = TravelPlannerGraph(enable_checkpointing=False)

        # 使用已有分析结果执行规划阶段
        final_state = await graph.run_planning_only(
            user_id=user_id,
            query=query,
            task_id=task_id,
            analysis_result=analysis_result,
            notification_service=notification_service
        )

        # 发送最终完成事件
        final_data = final_state.get("final_itinerary", {})
        if not final_data:
            # 如果没有最终行程，创建一个基本的响应
            final_data = {
                "title": f"旅行计划 - {task_id}",
                "message": "规划已完成",
                "task_id": task_id
            }

        # 存储最终结果到Redis
        await notification_service.store_final_result(task_id, final_data)

        # 发送完成事件
        await notification_service.notify_final_result(task_id, final_data)

        # 进行数据归档
        await notification_service.archive_task_data(task_id)

        logger.info(f"V2规划任务完成: {task_id}")

    except Exception as e:
        logger.error(f"V2规划任务失败: {task_id}, 错误: {str(e)}")
        await notification_service.notify_error(task_id, f"规划执行失败: {str(e)}")
        # 即使失败也要尝试归档错误信息
        try:
            await notification_service.archive_task_data(task_id)
        except Exception as archive_error:
            logger.error(f"归档失败任务数据时出错: {archive_error}")
