#!/usr/bin/env python3
"""
测试MySQL连接
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mysql_connection():
    """测试MySQL连接"""
    try:
        from src.database.mysql_client import get_db
        from src.models.mysql_crud import ai_planning_session_crud
        
        print("正在测试MySQL连接...")
        
        async with get_db() as db:
            print("✅ MySQL连接成功")
            
            # 测试简单查询
            result = await ai_planning_session_crud.get_by_user(db, user_id=1, limit=1)
            print(f"✅ 查询测试完成: {len(result)} 条记录")

            # 测试创建会话
            from datetime import datetime
            test_session_data = {
                'id': 'test_session_123',
                'user_id': 1,
                'status': 'PROCESSING',
                'user_input': {"query": "测试查询"},
                'created_at': datetime.now()
            }

            print("正在测试创建会话...")
            new_session = await ai_planning_session_crud.create_session(db, session_data=test_session_data)
            print(f"✅ 会话创建成功: {new_session.id}")
            
    except Exception as e:
        print(f"❌ MySQL连接失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mysql_connection())
