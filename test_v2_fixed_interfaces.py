"""
测试修复后的V2接口
验证task_id一致性和Redis数据流转
"""

import asyncio
import json
import httpx
import time
from typing import List, Dict, Any

# API配置
BASE_URL = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

class V2InterfaceTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = HEADERS
        self.task_id = None  # 存储意图分析阶段的task_id
    
    async def test_intent_analysis(self, query: str, user_id: str = "test_user_v2"):
        """
        测试V2意图识别接口
        
        验证：
        1. 生成统一格式的task_id
        2. 三方占坑机制
        3. 四个分析步骤的执行
        4. 分析结果存储到Redis
        """
        print("=" * 60)
        print("🔍 测试V2意图识别接口")
        print("=" * 60)
        
        url = f"{self.base_url}/api/travel/v2/intent/analyze"
        
        request_data = {
            "query": query,
            "user_id": user_id
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        print("\n开始接收SSE事件流...")
        
        events = []
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            async with client.stream("POST", url, json=request_data, headers=self.headers) as response:
                if response.status_code != 200:
                    print(f"❌ 请求失败: {response.status_code}")
                    print(f"错误信息: {await response.atext()}")
                    return None
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])  # 去掉 "data: " 前缀
                            events.append(data)
                            
                            event_type = data.get("event", "unknown")
                            print(f"\n📨 收到事件: {event_type}")
                            
                            if event_type == "step_start":
                                step_data = data.get("data", {})
                                print(f"   步骤开始: {step_data.get('step_name')} - {step_data.get('title')}")
                                print(f"   消息: {step_data.get('message')}")
                            
                            elif event_type == "step_end":
                                step_data = data.get("data", {})
                                print(f"   步骤结束: {step_data.get('step_name')} - {step_data.get('status')}")
                                result = step_data.get("result", {})
                                if result:
                                    print(f"   结果: {result.get('content', '')}")
                            
                            elif event_type == "complete":
                                complete_data = data.get("data", {})
                                print(f"   ✅ 分析完成!")
                                print(f"   消息: {complete_data.get('message')}")
                                # 提取task_id用于后续规划
                                self.task_id = complete_data.get("task_id")
                                if self.task_id:
                                    print(f"   🔑 获取到task_id: {self.task_id}")
                            
                            elif event_type == "error":
                                error_data = data.get("data", {})
                                print(f"   ❌ 错误: {error_data.get('message')}")
                            
                            elif event_type == "eos":
                                print("   🏁 事件流结束")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析错误: {e}")
                            print(f"原始数据: {line}")
        
        print(f"\n📊 总共收到 {len(events)} 个事件")
        return self.task_id
    
    async def test_planning_interface(self, task_id: str, user_id: str = "test_user_v2"):
        """
        测试V2立即规划接口
        
        验证：
        1. 通过task_id读取分析结果
        2. 执行规划流程
        3. 数据归档机制
        """
        print("=" * 60)
        print("🚀 测试V2立即规划接口")
        print("=" * 60)
        
        url = f"{self.base_url}/api/travel/v2/plan/stream"
        
        request_data = {
            "task_id": task_id,
            "user_id": user_id
        }
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        print("\n开始接收SSE事件流...")
        
        events = []
        
        async with httpx.AsyncClient(timeout=180.0) as client:
            async with client.stream("POST", url, json=request_data, headers=self.headers) as response:
                if response.status_code != 200:
                    print(f"❌ 请求失败: {response.status_code}")
                    print(f"错误信息: {await response.atext()}")
                    return False
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])
                            events.append(data)
                            
                            event_type = data.get("event", "unknown")
                            print(f"\n📨 收到事件: {event_type}")
                            
                            if event_type == "step_start":
                                step_data = data.get("data", {})
                                print(f"   步骤开始: {step_data.get('step_name')} - {step_data.get('title')}")
                                print(f"   消息: {step_data.get('message')}")
                            
                            elif event_type == "step_end":
                                step_data = data.get("data", {})
                                print(f"   步骤结束: {step_data.get('step_name')} - {step_data.get('status')}")
                            
                            elif event_type == "complete":
                                complete_data = data.get("data", {})
                                print(f"   ✅ 规划完成!")
                                print(f"   标题: {complete_data.get('title', '')}")
                                print(f"   消息: {complete_data.get('message', '')}")
                            
                            elif event_type == "error":
                                error_data = data.get("data", {})
                                print(f"   ❌ 错误: {error_data.get('message')}")
                                return False
                            
                            elif event_type == "eos":
                                print("   🏁 事件流结束")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析错误: {e}")
                            print(f"原始数据: {line}")
        
        print(f"\n📊 总共收到 {len(events)} 个事件")
        return True
    
    async def run_complete_test(self):
        """
        运行完整的测试流程
        """
        print("🧪 开始V2接口完整测试")
        print("测试目标：验证task_id一致性和数据流转机制")
        print("=" * 80)
        
        # 测试查询
        test_query = "我想去北京玩3天，喜欢历史文化和美食"
        
        # 第一步：测试意图识别接口
        task_id = await self.test_intent_analysis(test_query)
        
        if not task_id:
            print("❌ 意图识别测试失败，无法获取task_id")
            return False
        
        print(f"\n⏳ 等待5秒后开始规划测试...")
        await asyncio.sleep(5)
        
        # 第二步：测试立即规划接口
        success = await self.test_planning_interface(task_id)
        
        if success:
            print("\n🎉 V2接口测试完成！")
            print("✅ task_id一致性验证通过")
            print("✅ 数据流转机制验证通过")
            return True
        else:
            print("\n❌ V2接口测试失败")
            return False

async def main():
    """主函数"""
    tester = V2InterfaceTester()
    
    try:
        success = await tester.run_complete_test()
        if success:
            print("\n🏆 所有测试通过！")
        else:
            print("\n💥 测试失败！")
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
